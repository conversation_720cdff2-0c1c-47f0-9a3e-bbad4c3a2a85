{"name": "frontend", "version": "7.2.0", "private": true, "type": "module", "scripts": {"install:currency-converter": "cd ../packages/currency-converter && npm i && cd ../bookcars-helper && npm i", "install:reactjs-social-login": "cd ../packages/reactjs-social-login && npm i && cd ../../frontend", "install:dependencies": "npm run install:currency-converter && npm run install:reactjs-social-login", "ts:build": "npm run install:dependencies && tsc --build --verbose", "build": "npm run ts:build && cross-env NODE_OPTIONS=--max-old-space-size=1024 vite build", "dev": "npm run ts:build && vite", "ts:docker-build": "tsc --build --verbose", "dev:docker": "npm run ts:docker-build && vite", "preview": "vite preview", "fix": "eslint --fix .", "lint": "eslint . --cache --cache-location .eslintcache", "ncu": "ncu -u", "stylelint": "stylelint \"src/**/*.css\"", "stylelint:fix": "stylelint \"src/**/*.css\" --fix"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-data-grid": "^8.3.0", "@mui/x-date-pickers": "^8.3.0", "@paypal/react-paypal-js": "^8.8.3", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@types/leaflet": "^1.9.17", "@types/leaflet-boundary-canvas": "^1.0.3", "@types/nprogress": "^0.2.3", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@types/react-recaptcha-v3": "^1.1.5", "@types/react-slick": "^0.23.13", "@types/validator": "^13.15.0", "@vitejs/plugin-react": "^4.4.1", "axios": "^1.9.0", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "eslint-plugin-react-refresh": "^0.4.20", "github-fork-ribbon-css": "^0.2.3", "history": "^5.3.0", "leaflet": "^1.9.4", "leaflet-boundary-canvas": "^1.0.0", "localized-strings": "^2.0.3", "nprogress": "^0.2.0", "react": "^19.1.0", "react-circle-flags": "^0.0.23", "react-dom": "^19.1.0", "react-ga4": "^2.1.0", "react-hook-form": "^7.56.3", "react-leaflet": "^5.0.0", "react-router-dom": "^7.6.0", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "slick-carousel": "^1.8.1", "typescript": "^5.8.3", "validator": "^13.15.0", "vite": "^6.3.5", "zod": "^3.24.4"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.27.1", "@types/node": "^22.15.18", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "babel-plugin-react-compiler": "^19.1.0-rc.1", "eslint": "^9.26.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-compiler": "^19.1.0-rc.1", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.1.0", "npm-check-updates": "^18.0.1", "stylelint": "^16.19.1", "stylelint-config-standard": "^38.0.0", "terser": "^5.39.1", "vite-plugin-html": "^3.2.2"}}